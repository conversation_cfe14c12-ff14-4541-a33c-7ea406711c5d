# 测试环境配置
spring:
  profiles:
    active: test
  application:
    name: order-service-test
    
  # 数据源配置 - 使用测试数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**********:4000/order-center-test?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: root
    password: U4LsNiAkvGbmsmAK09
    
  # Redis配置
  redis:
    database: 10
    host: **********
    port: 6379
    password: 188288
    timeout: 5000ms
    
  # 云配置
  cloud:
    nacos:
      config:
        file-extension: yml
        server-addr: **********:8848
        shared-configs:
          - data-id: common.yml
            refresh: true
      discovery:
        server-addr: **********:8848
    sentinel:
      eager: true
      transport:
        dashboard: **********:8718

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  type-aliases-package: com.wnkx.order.domain
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# Feign 配置 - 指向测试环境服务
feign:
  sentinel:
    enabled: false  # 测试环境关闭熔断
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        loggerLevel: full  # 开启详细日志
      # 配置模特服务的测试地址
      biz-center:
        url: http://***********:7001  # 测试环境的业务中心服务地址
        connectTimeout: 30000
        readTimeout: 30000
        loggerLevel: full
  compression:
    request:
      enabled: true
    response:
      enabled: true
      useGzipDecoder: true

# 日志配置
logging:
  level:
    com.wnkx.order: DEBUG
    com.wnkx.biz: DEBUG
    org.springframework: WARN
    feign: DEBUG
    root: INFO

# 测试环境特定配置
wnkx:
  ds:
    ip: **********
    username: root
    password: U4LsNiAkvGbmsmAK09
    port: 4000
    biz-db: biz-center-test
    order-db: order-center-test
    user-db: user-center-test
    job-db: wnkx-job-test

# 订单相关测试配置
order:
  video:
    # 测试环境缩短时间配置
    orderAutoFinishedTime: 1
    releaseOverTime: 1
    preselectModelOverTime: 72
    merchantApplyRefundOverTime: 1
    reminderTime: 1
    orderFeedbackOverdueTime: 1
    closeOrderHours: 720
    showReopenOrder: 30
    preselectModelNumberOfPits: 2
    preselectModelReserveTime: 24

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
