<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 测试环境日志级别配置 -->
    <logger name="com.wnkx.order" level="DEBUG"/>
    <logger name="com.wnkx.biz" level="DEBUG"/>
    <logger name="feign" level="DEBUG"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="com.mysql" level="WARN"/>
    <logger name="org.mybatis" level="WARN"/>
    
    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
