# 测试环境启动配置
spring:
  application:
    name: order-service-test
  profiles:
    active: test
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        file-extension: yml
        server-addr: 10.160.0.1:8848
        namespace: # 使用默认命名空间
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: common.yml
            refresh: true
            group: DEFAULT_GROUP
      discovery:
        server-addr: 10.160.0.1:8848
        namespace: # 使用默认命名空间
        group: DEFAULT_GROUP
        enabled: true
    sentinel:
      eager: false  # 测试环境不启用sentinel
      transport:
        dashboard: 10.160.0.1:8718

# 管理端点配置
management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: '*'
